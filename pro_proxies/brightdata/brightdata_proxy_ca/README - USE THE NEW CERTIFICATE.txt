Please use the *new* SSL certificate for all new projects and avoid using the previous certificate. the new certificate is effective from September 2024

IMPORTANT: to use the *new* certificate, you must use port 33335 when connecting to Bright Data's proxy network, even if documentation and sample codes provided mention port 22225. Documentation will gradually be updated to port 33335 as the old certificate is being phased out.

You have a few options to use this certificate:

- If you write code, you can simply load it into your existing code.
- If you are using Windows and third-party tools that allow you to configure proxy settings (like AdsPower, Proxifier, Multilogin, Undetectable etc.) you can install the certificate on your computer using the installer.

Again, remember: when using the new certificate you *MUST use port 33335* when connecting to Bright Data's proxy network, even if documentation and sample codes provided mention port 22225. 

Full information regarding how to use the SSL certificate is available on https://docs.brightdata.com/general/account/ssl-certificate

--------------------------------------------------------------------------

The old SSL certificate will expire in 2026 and is being phased out. 

**Please DO NOT use the old certificate for new projects**

Again, Keep in mind that the new certificate requires you to use port 33335, meaning your proxy host will be from now on: brd.superproxy.io:33335