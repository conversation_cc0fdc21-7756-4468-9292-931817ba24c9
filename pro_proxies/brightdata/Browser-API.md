## Browser API endpoint
If you haven't yet used our Browser API before, here are the key details to help you get started:
 1. Remove proxy IP configurations from your code, they are no longer needed.
 2. Configure your code to connect to the Browser API endpoint (instead of using a local browser), using the following URL with embedded credentials (auth+@host):

Puppeteer / Playwright:
wss://brd-customer-hl_72945ad3-zone-scraping_browser1:<EMAIL>:9222


Selenium:
https://brd-customer-hl_72945ad3-zone-scraping_browser1:<EMAIL>:9515

## Security settings

Password
vzai55ui5mmm

## Access Details
Host:
brd.superproxy.io

Ports:
9222 (Default)

9515 (Selenium)

Username:
Control your proxy by 
modifying the username
brd-customer-hl_72945ad3-zone-scraping_browser1

Passwords:
vzai55ui5mmm

Single string formats:
host:port:username:password

username:password@host:port

host,port,username,password

## Code Examples:Getting started
Install required library
Choose your preferred language and browser navigation library below to continue.
Node.js, Puppeteer
INSTALL REQUIRED LIBRARY

npm install puppeteer-core
Run example script
Node.js, Puppeteer
CODE

const puppeteer = require('puppeteer-core');

async function run(){
    const BROWSER_WS = "wss://brd-customer-hl_72945ad3-zone-scraping_browser1:<EMAIL>:9222";
    try {
        console.log('Connecting to Browser API...');
        const browser = await puppeteer.connect({
            browserWSEndpoint: BROWSER_WS,
        });
        const page = await browser.newPage();
        await page.goto('https://www.example.com');
        // CAPTCHA handling: If you're expecting a CAPTCHA on the target page, use the following code snippet to check the status of Browser API's automatic CAPTCHA solver
        // const client = await page.createCDPSession();
        // console.log('Waiting captcha to solve...');
        // const { status } = await client.send('Captcha.waitForSolve', {
        //   detectTimeout: 10000,
        // });
        // console.log('Captcha solve status:', status);
        const html = await page.content();
        console.log(html);
        await browser.close();
    } catch (error) {
        console.log(error)
    }
}

run()

Get page HTML
Node.js, Puppeteer
CODE

const page = await browser.newPage();
await page.goto('https://example.com');
const html = await page.content();

Click on element
Node.js, Puppeteer
CODE

const page = await page.newPage();
await page.goto('https://example.com');
await page.click('a[href]');

Take screenshot
Node.js, Puppeteer
CODE

// More info at https://pptr.dev/api/puppeteer.page.screenshot
await page.screenshot({path: 'screenshot.png'});

Set Cookies
Please note that this is supported for 
KYC-approved
 customers only.
Node.js, Puppeteer
CODE

const page = await browser.newPage();
await page.setCookie({
    name: 'LANG',
    value: 'en-US',
    domain: 'example.com',
});
await page.goto('https://example.com');

Blocking endpoints
It is possible to block endpoints that are not required to save bandwidth. See an example of this below:
Node.js, Puppeteer
CODE

// connect to a remote browser...
const urls = ['*doubleclick.net*'];
const page = await browser.newPage();
const client = await page.target().createCDPSession();
await client.send('Network.enable');
await client.send('Network.setBlockedURLs', {urls});
await page.goto('https://washingtonpost.com');

Blocking endpoints
When using the Browser API, the same country-targeting parameter is available to use as in our other proxy products.
When setting up your script, add the "-country" flag, after your "USER" credentials within the Bright Data endpoint, followed by the 2-letter 
ISO code
 for that country.
Node.js, Puppeteer
CODE

const SBR_WS_ENDPOINT = `wss://${USER-country-us:PASS}@brd.superproxy.io:9222`;
In the example above, we added "-country-us" to the Bright Data endpoint within our script, so our request will originate from the United States ("us")
EU region
You can target the entire European Union region in the same manner as "Country" above by adding "eu" after "country" in your request: "-country-eu". Requests sent using -country-eu, will use IPs from one of the countries below which are included automatically within "eu":
EU COUNTRIES

AL, AZ, KG, BA, UZ, BI, XK, SM, DE, AT, CH, UK, GB,IE, IM, FR, ES, NL, IT, PT, BE, AD, MT, MC, MA, LU, TN, DZ, GI, LI, SE, DK, FI, NO, AX, IS, GG, JE, EU, GL, VA, FX, FO