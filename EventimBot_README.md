# EventimBot - Wiederhergestellter Quellcode

## Beschreibung
EventimBot ist ein automatisierter Ticket-Buchungsbot für verschiedene Ticket-Plattformen.

## Kontakt
- **WhatsApp**: +212 622 056197
- **Email**: <EMAIL>


```
EventimBot/
├── eventimbot_main.js              ← HAUPTDATEI
├── package.json                    ← Korrekte Dependencies
├── package-lock.json               ← Neue Lock-Datei
├── EventimBot_README.md            ← Dokumentation
├── handlers/                       ← Website-Handler
│   ├── eventim.js                 ← Eventim.de Handler
│   ├── eventim-light.js           ← Eventim-Light.com Handler
│   ├── muenchenticket-de.js       ← MuenchenTicket.de Handler
│   └── muenchenticket-net.js      ← MuenchenTicket.net Handler
├── events/                         ← Event-Listen
│   ├── eventim.de.txt
│   ├── eventim-light.com.txt
│   ├── muenchenticket.de_tickets.txt
│   └── muenchenticket.net_shop.txt
├── browser/                        ← Chrome für Puppeteer
│   └── chrome.exe + Dateien
├── proxys.txt                      ← Proxy-Liste
└── proxy_auth.txt                  ← Proxy-Authentifizierung
```


## Unterstützte Websites
1. eventim.de
2. eventim-light.com
3. muenchenticket.de/tickets
4. muenchenticket.net/shop

## Installation
```bash
npm install
```

## Konfiguration
1. Erstelle `proxys.txt` mit einer Proxy-Liste (ein Proxy pro Zeile)
2. Erstelle `proxy_auth.txt` mit Proxy-Authentifizierung:
   ```
   username:your_username
   password:your_password
   ```
3. Erstelle Event-Listen in `./events/`:
   - `eventim.de.txt`
   - `eventim-light.com.txt`
   - `muenchenticket.de_tickets.txt`
   - `muenchenticket.net_shop.txt`

## Verwendung

### 1. Proxys scrapen (ZUERST ausführen)
```bash
npm run scrape-proxies
```

### 2. Bot starten
```bash
npm start
```

### 3. Proxy-Kompatibilität testen (optional)
```bash
npm run test-proxy-compatibility
```

### 4. Brightdata-Verbindung testen (optional)
```bash
npm run test-brightdata
```

## Struktur
- `index.js` - Hauptdatei
- `handlers/` - Website-spezifische Handler
  - `eventim.js` - Eventim.de Handler
  - `eventim-light.js` - Eventim-Light.com Handler
  - `muenchenticket-de.js` - MuenchenTicket.de Handler
  - `muenchenticket-net.js` - MuenchenTicket.net Handler

## Features
- Proxy-Rotation für IP-Wechsel
- Automatische Cookie-Banner-Entfernung
- Zufällige Event-Auswahl
- Konfigurierbare Ticket-Anzahl
- Wartezeiten zwischen Events
- Error-Handling für "Access Denied"
- Resource-Blocking (Bilder, Analytics, Werbung)
- Live-Datenverbrauch-Monitoring

## Hinweise
- Der Bot verwendet Puppeteer mit Stealth-Plugin
- Chrome-Browser wird aus `./browser/chrome.exe` geladen
- Alle Handler sind modular aufgebaut


# 🎯 **Neu implementiert:**

## 🌟 **Brightdata Browser API Integration (NEU)**
### Professionelle Proxy-Lösung
- **Automatisches Proxy-Management** ohne manuelle Konfiguration
- **Integrierte Stealth-Mechanismen** für höhere Erfolgsrate
- **Automatische CAPTCHA-Lösung** durch Brightdata
- **Fallback-System** zu Free Proxys bei Verbindungsproblemen
- **Nahtlose Integration** - alle Handler funktionieren unverändert
- **WebSocket-basiert** für optimale Performance

**Alle Details:**
- `BRIGHTDATA_README.md`
- Test: `npm run test-brightdata`

## 🆕 Weitere Features
### 📊 Datenverbrauch-Optimierung für bezahlte Proxys
- **60-80% Dateneinsparung** ohne Funktionalitätsverlust
- **Drei Optimierungslevel**: minimal, moderate, aggressive
- **Intelligentes Resource-Blocking** (Bilder, Analytics, Werbung)
- **Eventim-spezifische Optimierungen** (kritische Ressourcen werden nie blockiert)
- **Live-Monitoring** des Datenverbrauchs
- **Automatische Bot-Detection-Vermeidung**
- **Kosten-Schätzungen** für verschiedene Proxy-Anbieter

**Alle Details:**
- `DATA_OPTIMIZATION_SUMMARY.md`
- `DATA_OPTIMIZATION_README.md`

### Manuelles Free Proxy System - Proxy-Management
- **Manueller Proxy-Scraper** für kontrollierte Proxy-Beschaffung
- **Proxy-Kompatibilitäts-Tester** für Eventim-spezifische Tests
- **Proxy-Testing** vor der Verwendung
- **Intelligente Proxy-Rotation** mit defekten Proxy-Entfernung
- **Länder-spezifische Proxys** (DE, AT, CH)
- **Keine Authentifizierung** mehr erforderlich
- **Paralleles Proxy-Testing** für bessere Performance

**Alle Details:**
- `PROXY_README.md`
- `PROXY_WORKFLOW.md`

## 📝 **Neuer Workflow:**

### Free Proxies
#### Schritt 1: Free Proxys scrapen
```bash
npm run scrape-proxies
```
Dieses Tool scrapt Proxys von verschiedenen Quellen und speichert funktionierende Proxys in `proxys.txt`.

#### Schritt 2: Proxy-Kompatibilität testen (empfohlen)
```bash
npm run test-proxy-compatibility
```
Testet die Proxys speziell mit Eventim-URLs und erstellt einen Kompatibilitäts-Report.

#### Schritt 3: Bot starten
```bash
npm start
```
Der Bot lädt die Proxys aus `proxys.txt` und startet ohne Verzögerung.

#### Schritt 4: Datenverbrauch-Monitor (optional)
```bash
npm run data-monitor
```
Testet verschiedene Optimierungslevel und zeigt Dateneinsparungen an.

# ⚠️ Wichtige Hinweise
**Der Bot benötigt zwingend funktionierende Proxys!** Das Konzept basiert darauf, mit verschiedenen IP-Adressen mehrfach Tickets in den Warenkorb zu legen. Ohne Proxys kann der Bot nicht funktionieren.

### 🔍 **Proxy-Problem-Diagnose:**
Die Logs zeigen typische Proxy-Probleme mit HTTPS-Websites:
- `ERR_PROXY_CONNECTION_FAILED` - Proxy antwortet nicht
- `ERR_TUNNEL_CONNECTION_FAILED` - HTTPS-Tunnel kann nicht aufgebaut werden
- `ERR_CERT_AUTHORITY_INVALID` - Zertifikatsprobleme

Diese Probleme treten auf, weil viele Free Proxys nicht für HTTPS-Verbindungen geeignet sind.
