# EventimBot - Brightdata Browser API Integration

## Übersicht

Diese Version des EventimBots unterstützt sowohl die **Brightdata Browser API** als auch das traditionelle **Free Proxy System**. Die Brightdata-Integration bietet professionelle Proxy-Services mit erweiterten Features.

## 🌟 Brightdata Browser API Features

### Vorteile
- **Automatisches Proxy-Management**: Keine manuelle Proxy-Konfiguration erforderlich
- **Integrierte Stealth-Mechanismen**: Sehr effektive Anti-Detection-Features
- **Automatische CAPTCHA-Lösung**: Integrierte CAPTCHA-Solver
- **Höhere Erfolgsrate**: Professionelle Proxy-Infrastruktur
- **Keine Proxy-Ausfälle**: Zuverlässige Verbindungen

### Technische Details
- **Deutschland Endpoint**: `wss://brd-customer-hl_72945ad3-zone-scraping_browser1-country-de:<EMAIL>:9222`
- **EU Fallback Endpoint**: `wss://brd-customer-hl_72945ad3-zone-scraping_browser1-country-eu:<EMAIL>:9222`
- **Verwendet**: `puppeteer-core` für optimale Kompatibilität
- **Intelligenter Fallback**: Automatischer Wechsel zwischen DE/EU Endpoints und zu Free Proxys
- **Fehlerbehandlung**: Automatische Deaktivierung bei wiederholten Access Denied-Fehlern

## 🔧 Konfiguration

### 1. Brightdata aktivieren
Beim Start des Bots erscheint ein Konfigurationsmenü:

```
🌟 Brightdata Browser API Konfiguration
==========================================
Aktueller Status: ❌ Deaktiviert

Optionen:
1. Brightdata Browser API aktivieren (empfohlen für professionelle Nutzung)
2. Lokale Browser mit Free Proxys verwenden
3. Weiter ohne Änderung
```

### 2. Datenoptimierung
Nach der Brightdata-Konfiguration können Sie die Datenoptimierung einstellen:

```
📊 Datenverbrauch-Optimierung
================================
Verfügbare Optimierungslevel:
1. minimal    - Nur offensichtlich unnötige Ressourcen blockieren
2. moderate   - Balance zwischen Datensparen und Funktionalität (empfohlen)
3. aggressive - Maximale Dateneinsparung
```

## 🚀 Verwendung

### Mit Brightdata (empfohlen)
1. Bot starten: `npm start`
2. Option 1 wählen: "Brightdata Browser API aktivieren"
3. Datenoptimierung wählen (empfohlen: moderate)
4. Website auswählen und Bot starten

### Mit Free Proxys (Fallback)
1. Proxys scrapen: `npm run scrape-proxies`
2. Bot starten: `npm start`
3. Option 2 wählen: "Lokale Browser mit Free Proxys verwenden"
4. Datenoptimierung wählen
5. Website auswählen und Bot starten

## 🔄 Intelligenter Fallback-Mechanismus

### Automatischer Endpoint-Wechsel
Der Bot wechselt automatisch zwischen Endpoints:
1. **Deutschland Endpoint** (Primär)
2. **EU Fallback Endpoint** (Sekundär)
3. **Free Proxy System** (Letzter Fallback)

### Fallback-Trigger
Der Bot wechselt automatisch zu Free Proxys, wenn:
- Brightdata-Verbindung fehlschlägt
- WebSocket-Endpoint nicht erreichbar ist
- Authentifizierungsprobleme auftreten
- **3 aufeinanderfolgende Access Denied-Fehler** auftreten

### Intelligente Fehlerbehandlung
- **Fehlerzähler**: Verfolgt aufeinanderfolgende Fehler
- **Automatische Zurücksetzung**: Bei erfolgreichem Zugriff
- **Sanfter Übergang**: Nahtloser Wechsel ohne Bot-Neustart

## 📊 Unterschiede zwischen den Modi

| Feature | Brightdata API | Free Proxys |
|---------|----------------|-------------|
| Proxy-Management | Automatisch | Manuell |
| CAPTCHA-Lösung | Integriert | Keine |
| Stealth-Features | Professionell | Basic |
| Erfolgsrate | Hoch | Variabel |
| Kosten | Bezahlt | Kostenlos |
| Setup-Aufwand | Minimal | Hoch |

## 🛠 Technische Implementierung

### Kernlogik bleibt unverändert
Die Handler (`eventim.js`, `muenchenticket-de.js`, etc.) verwenden weiterhin die gleiche Puppeteer-API:
- `page.waitForSelector()`
- `page.click()`
- `page.goto()`
- `page.evaluate()`

### Änderungen in der Browser-Erstellung
```javascript
// Brightdata-Modus
const browser = await puppeteerCore.connect({
    browserWSEndpoint: BRIGHTDATA_CONFIG.endpoint
});

// Free Proxy-Modus (Fallback)
const browser = await puppeteerExtra.launch({
    args: ['--proxy-server=' + proxy]
});
```

### Intelligente Proxy-Verwaltung
```javascript
// Proxy nur holen wenn Brightdata nicht aktiviert ist
if (!isBrightdataEnabled()) {
    randomProxy = proxyManager.getRandomProxy();
}

const browser = await launchBrowser(randomProxy);
```

## 📝 Logs und Monitoring

### Brightdata-Logs
```
🌟 Verbinde mit Brightdata Browser API (Level: moderate)...
✅ Erfolgreich mit Brightdata Browser API verbunden
```

### Fallback-Logs
```
❌ Fehler beim Verbinden mit Brightdata: Connection failed
🔄 Versuche anderen Brightdata Endpoint...
🌍 Verwende EU Endpoint (Versuch 2)
⚠️ Brightdata Access Denied (3/3)
🚫 Zu viele Access Denied-Fehler mit Brightdata
🔄 Wechsle automatisch zu Free Proxy System...
🚀 Starte lokalen Browser mit Proxy (Level: moderate)...
```

## 🔒 Sicherheit

- **Credentials**: In der Konfiguration eingebettet
- **Verschlüsselung**: WebSocket Secure (WSS)
- **Authentifizierung**: Automatisch über Endpoint-URL

## 🔧 Troubleshooting

### Häufige Probleme und Lösungen

#### Problem: "Access Denied" bei Brightdata
**Ursache**: Eventim blockiert bestimmte Brightdata-IP-Ranges
**Lösung**:
- Bot wechselt automatisch zu Free Proxys nach 3 Fehlern
- Teste mit: `npm run test-brightdata`
- Verwende EU Endpoint als Alternative

#### Problem: WebSocket-Verbindung fehlgeschlagen
**Ursache**: Netzwerk- oder Firewall-Probleme
**Lösung**:
- Prüfe Internetverbindung
- Teste beide Endpoints: DE und EU
- Fallback zu Free Proxys wird automatisch aktiviert

#### Problem: Brightdata funktioniert, aber langsam
**Lösung**:
- Verwende "minimal" Datenoptimierung für bessere Performance
- Deutschland Endpoint ist meist schneller als EU

### Debug-Befehle
```bash
# Teste Brightdata-Verbindung
npm run test-brightdata

# Starte Bot mit Debug-Ausgabe
DEBUG=* npm start

# Teste nur Free Proxys
npm run scrape-proxies && npm start
```

## 📞 Support

Bei Problemen mit der Brightdata-Integration:
- **WhatsApp**: +212 622 056197
- **Email**: <EMAIL>

## 🔄 Migration von Free Proxys

Bestehende Nutzer können nahtlos wechseln:
1. Keine Änderungen an Event-Listen erforderlich
2. Bestehende Proxy-Listen bleiben als Fallback verfügbar
3. Alle Handler funktionieren ohne Änderungen
