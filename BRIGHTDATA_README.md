# EventimBot - Brightdata Browser API Integration

## Übersicht

Diese Version des EventimBots unterstützt sowohl die **Brightdata Browser API** als auch das traditionelle **Free Proxy System**. Die Brightdata-Integration bietet professionelle Proxy-Services mit erweiterten Features.

## 🌟 Brightdata Browser API Features

### Vorteile
- **Automatisches Proxy-Management**: Keine manuelle Proxy-Konfiguration erforderlich
- **Integrierte Stealth-Mechanismen**: Sehr effektive Anti-Detection-Features
- **Automatische CAPTCHA-Lösung**: Integrierte CAPTCHA-Solver
- **Höhere Erfolgsrate**: Professionelle Proxy-Infrastruktur
- **Keine Proxy-Ausfälle**: Zuverlässige Verbindungen

### Technische Details
- **WebSocket Endpoint**: `wss://brd-customer-hl_72945ad3-zone-scraping_browser1:<EMAIL>:9222`
- **Verwendet**: `puppeteer-core` für optimale Kompatibilität
- **Fallback**: Automatischer Wechsel zu lokalem Proxy-System bei Verbindungsproblemen

## 🔧 Konfiguration

### 1. Brightdata aktivieren
Beim Start des Bots erscheint ein Konfigurationsmenü:

```
🌟 Brightdata Browser API Konfiguration
==========================================
Aktueller Status: ❌ Deaktiviert

Optionen:
1. Brightdata Browser API aktivieren (empfohlen für professionelle Nutzung)
2. Lokale Browser mit Free Proxys verwenden
3. Weiter ohne Änderung
```

### 2. Datenoptimierung
Nach der Brightdata-Konfiguration können Sie die Datenoptimierung einstellen:

```
📊 Datenverbrauch-Optimierung
================================
Verfügbare Optimierungslevel:
1. minimal    - Nur offensichtlich unnötige Ressourcen blockieren
2. moderate   - Balance zwischen Datensparen und Funktionalität (empfohlen)
3. aggressive - Maximale Dateneinsparung
```

## 🚀 Verwendung

### Mit Brightdata (empfohlen)
1. Bot starten: `npm start`
2. Option 1 wählen: "Brightdata Browser API aktivieren"
3. Datenoptimierung wählen (empfohlen: moderate)
4. Website auswählen und Bot starten

### Mit Free Proxys (Fallback)
1. Proxys scrapen: `npm run scrape-proxies`
2. Bot starten: `npm start`
3. Option 2 wählen: "Lokale Browser mit Free Proxys verwenden"
4. Datenoptimierung wählen
5. Website auswählen und Bot starten

## 🔄 Automatischer Fallback

Der Bot wechselt automatisch zu Free Proxys, wenn:
- Brightdata-Verbindung fehlschlägt
- WebSocket-Endpoint nicht erreichbar ist
- Authentifizierungsprobleme auftreten

## 📊 Unterschiede zwischen den Modi

| Feature | Brightdata API | Free Proxys |
|---------|----------------|-------------|
| Proxy-Management | Automatisch | Manuell |
| CAPTCHA-Lösung | Integriert | Keine |
| Stealth-Features | Professionell | Basic |
| Erfolgsrate | Hoch | Variabel |
| Kosten | Bezahlt | Kostenlos |
| Setup-Aufwand | Minimal | Hoch |

## 🛠 Technische Implementierung

### Kernlogik bleibt unverändert
Die Handler (`eventim.js`, `muenchenticket-de.js`, etc.) verwenden weiterhin die gleiche Puppeteer-API:
- `page.waitForSelector()`
- `page.click()`
- `page.goto()`
- `page.evaluate()`

### Änderungen in der Browser-Erstellung
```javascript
// Brightdata-Modus
const browser = await puppeteerCore.connect({
    browserWSEndpoint: BRIGHTDATA_CONFIG.endpoint
});

// Free Proxy-Modus (Fallback)
const browser = await puppeteerExtra.launch({
    args: ['--proxy-server=' + proxy]
});
```

### Intelligente Proxy-Verwaltung
```javascript
// Proxy nur holen wenn Brightdata nicht aktiviert ist
if (!isBrightdataEnabled()) {
    randomProxy = proxyManager.getRandomProxy();
}

const browser = await launchBrowser(randomProxy);
```

## 📝 Logs und Monitoring

### Brightdata-Logs
```
🌟 Verbinde mit Brightdata Browser API (Level: moderate)...
✅ Erfolgreich mit Brightdata Browser API verbunden
```

### Fallback-Logs
```
❌ Fehler beim Verbinden mit Brightdata: Connection failed
🔄 Fallback zu lokalem Browser mit Proxy...
🚀 Starte lokalen Browser mit Proxy (Level: moderate)...
```

## 🔒 Sicherheit

- **Credentials**: In der Konfiguration eingebettet
- **Verschlüsselung**: WebSocket Secure (WSS)
- **Authentifizierung**: Automatisch über Endpoint-URL

## 📞 Support

Bei Problemen mit der Brightdata-Integration:
- **WhatsApp**: +212 622 056197
- **Email**: <EMAIL>

## 🔄 Migration von Free Proxys

Bestehende Nutzer können nahtlos wechseln:
1. Keine Änderungen an Event-Listen erforderlich
2. Bestehende Proxy-Listen bleiben als Fallback verfügbar
3. Alle Handler funktionieren ohne Änderungen
