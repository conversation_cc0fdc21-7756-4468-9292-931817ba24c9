
const puppeteerExtra = require('puppeteer-extra').use(require('puppeteer-extra-plugin-stealth')());
const puppeteerCore = require('puppeteer-core');
const readlineSync = require('readline-sync');
const ProxyManager = require('./proxy-manager');
const ResourceBlocker = require('./resource-blocker');
const DataOptimizationConfig = require('./data-optimization-config');

// Globale Variablen
let proxyManager;
let errorMessages = ['HTTP ERROR', 'Access Denied'];
let dataOptimizationLevel = 'moderate'; // Standard-Optimierungslevel

// Brightdata Browser API Konfiguration
const BRIGHTDATA_CONFIG = {
    // Deutschland-spezifische IPs für bessere Eventim-Kompatibilität
    endpoint: 'wss://brd-customer-hl_72945ad3-zone-scraping_browser1-country-de:<EMAIL>:9222',
    fallbackEndpoint: 'wss://brd-customer-hl_72945ad3-zone-scraping_browser1-country-eu:<EMAIL>:9222',
    enabled: false, // Standardmäßig deaktiviert, kann über Menü aktiviert werden
    failureCount: 0, // Zähler für aufeinanderfolgende Fehler
    maxFailures: 3 // Maximale Fehler bevor Fallback zu Free Proxys
};

// Initialisiere Proxy-Manager (ohne automatisches Laden)
function initProxyManager() {
    console.log('🚀 Initialisiere Free Proxy Manager...');
    proxyManager = new ProxyManager(false); // false = kein automatisches Laden
    console.log('✅ Proxy Manager gestartet (manueller Modus)');
}

// Legacy-Funktion für Rückwärtskompatibilität
function loadConfig() {
    // Diese Funktion wird nicht mehr benötigt, da der ProxyManager
    // automatisch die Proxys verwaltet
    console.log('ℹ️ Proxy-Konfiguration wird automatisch vom ProxyManager verwaltet');
}

// Hilfsfunktionen
async function sleep(seconds) {
    return new Promise(resolve => setTimeout(resolve, seconds * 1000));
}

async function clickElement(page, selector) {
    const element = await page.$(selector);
    await element.click().catch(error => console.log(error));
}

async function launchBrowser(proxy = null, optimizationLevel = null) {
    const level = optimizationLevel || dataOptimizationLevel;
    const config = DataOptimizationConfig;

    if (BRIGHTDATA_CONFIG.enabled) {
        console.log(`🌟 Verbinde mit Brightdata Browser API (Level: ${level})...`);

        // Wähle Endpoint basierend auf Failure Count
        const currentEndpoint = BRIGHTDATA_CONFIG.failureCount < 2
            ? BRIGHTDATA_CONFIG.endpoint
            : BRIGHTDATA_CONFIG.fallbackEndpoint;

        const endpointType = currentEndpoint.includes('country-de') ? 'DE' : 'EU';
        console.log(`🌍 Verwende ${endpointType} Endpoint (Versuch ${BRIGHTDATA_CONFIG.failureCount + 1})`);

        try {
            // Verwende puppeteer-core für Brightdata
            const browser = await puppeteerCore.connect({
                browserWSEndpoint: currentEndpoint,
                ignoreHTTPSErrors: true,
                timeout: 60000
            });

            console.log(`✅ Erfolgreich mit Brightdata Browser API verbunden (${endpointType})`);
            // Reset failure count bei erfolgreicher Verbindung
            BRIGHTDATA_CONFIG.failureCount = 0;
            return browser;

        } catch (error) {
            console.error('❌ Fehler beim Verbinden mit Brightdata:', error.message);
            BRIGHTDATA_CONFIG.failureCount++;

            if (BRIGHTDATA_CONFIG.failureCount >= BRIGHTDATA_CONFIG.maxFailures) {
                console.log('⚠️ Zu viele Brightdata-Fehler, wechsle zu Free Proxys...');
                BRIGHTDATA_CONFIG.enabled = false;
                BRIGHTDATA_CONFIG.failureCount = 0;
            } else {
                console.log(`🔄 Versuche anderen Brightdata Endpoint...`);
                // Rekursiver Aufruf mit anderem Endpoint
                return await launchBrowser(proxy, optimizationLevel);
            }
        }
    }

    // Alte Proxy-Methode (Fallback oder wenn Brightdata deaktiviert)
    console.log(`🚀 Starte lokalen Browser mit Proxy (Level: ${level})...`);

    if (!proxy) {
        throw new Error('Proxy erforderlich für lokalen Browser-Modus');
    }

    // Basis Chrome-Flags
    let chromeArgs = [
        '--no-first-run',
        '--proxy-server=' + proxy,
        '--no-sandbox',
        '--disable-setuid-sandbox'
    ];

    // Füge Datenverbrauch-Optimierungen hinzu
    chromeArgs = chromeArgs.concat(config.browserOptimizations.chromeFlags);

    // Bei aggressiver Optimierung zusätzliche Flags
    if (level === 'aggressive') {
        chromeArgs = chromeArgs.concat(config.browserOptimizations.aggressiveFlags);
    }

    return await puppeteerExtra.launch({
        // Verwende System-Chrome/Chromium (entferne executablePath für automatische Erkennung)
        'headless': false,
        'defaultViewport': null,
        'ignoreHTTPSErrors': true,
        'timeout': 60000,
        'args': chromeArgs
    });
}

// Neue Funktion: Erstelle optimierte Page mit Resource-Blocking
async function createOptimizedPage(browser, optimizationLevel = null) {
    const level = optimizationLevel || dataOptimizationLevel;
    const page = await browser.newPage();

    // Initialisiere Resource-Blocker
    const resourceBlocker = new ResourceBlocker(level);
    await resourceBlocker.setupResourceBlocking(page);

    // Speichere Resource-Blocker in Page für spätere Statistiken
    page._resourceBlocker = resourceBlocker;

    return page;
}



// Funktion zum Setzen des Optimierungslevels
function setDataOptimizationLevel(level) {
    if (DataOptimizationConfig.levels[level]) {
        dataOptimizationLevel = level;
        console.log(`🔧 Datenoptimierung geändert zu: ${level}`);
    } else {
        console.log(`❌ Unbekanntes Optimierungslevel: ${level}`);
        console.log(`Verfügbare Level: ${Object.keys(DataOptimizationConfig.levels).join(', ')}`);
    }
}

// Funktionen für Brightdata-Konfiguration
function enableBrightdata() {
    BRIGHTDATA_CONFIG.enabled = true;
    console.log('🌟 Brightdata Browser API aktiviert');
    console.log('ℹ️ Proxy-Management wird automatisch von Brightdata übernommen');
}

function disableBrightdata() {
    BRIGHTDATA_CONFIG.enabled = false;
    console.log('🚫 Brightdata Browser API deaktiviert');
    console.log('ℹ️ Verwende lokale Browser mit Proxy-Rotation');
}

function isBrightdataEnabled() {
    return BRIGHTDATA_CONFIG.enabled;
}

// Funktion zum Behandeln von Access Denied bei Brightdata
function handleBrightdataAccessDenied() {
    if (BRIGHTDATA_CONFIG.enabled) {
        BRIGHTDATA_CONFIG.failureCount++;
        console.log(`⚠️ Brightdata Access Denied (${BRIGHTDATA_CONFIG.failureCount}/${BRIGHTDATA_CONFIG.maxFailures})`);

        if (BRIGHTDATA_CONFIG.failureCount >= BRIGHTDATA_CONFIG.maxFailures) {
            console.log('🚫 Zu viele Access Denied-Fehler mit Brightdata');
            console.log('🔄 Wechsle automatisch zu Free Proxy System...');
            BRIGHTDATA_CONFIG.enabled = false;
            BRIGHTDATA_CONFIG.failureCount = 0;
            return true; // Signalisiert, dass zu Free Proxys gewechselt wurde
        }
    }
    return false;
}

// Funktion zum Zurücksetzen der Brightdata Failure Count bei Erfolg
function resetBrightdataFailures() {
    if (BRIGHTDATA_CONFIG.enabled && BRIGHTDATA_CONFIG.failureCount > 0) {
        console.log('✅ Brightdata funktioniert wieder, setze Fehlerzähler zurück');
        BRIGHTDATA_CONFIG.failureCount = 0;
    }
}

// Exportiere Hilfsfunktionen für Handler
module.exports = {
    sleep,
    clickElement,
    launchBrowser,
    createOptimizedPage,
    setDataOptimizationLevel,
    enableBrightdata,
    disableBrightdata,
    isBrightdataEnabled,
    handleBrightdataAccessDenied,
    resetBrightdataFailures,
    get proxyManager() { return proxyManager; },
    get errorMessages() { return errorMessages; },
    get dataOptimizationLevel() { return dataOptimizationLevel; }
};

// Website-Handler werden nach ProxyManager-Initialisierung geladen
let EventimHandler, EventimLightHandler, MuenchenTicketDEHandler, MuenchenTicketNETHandler;

function loadHandlers() {
    EventimHandler = require('./handlers/eventim');
    EventimLightHandler = require('./handlers/eventim-light');
    MuenchenTicketDEHandler = require('./handlers/muenchenticket-de');
    MuenchenTicketNETHandler = require('./handlers/muenchenticket-net');
}

// Brightdata-Konfigurationsmenü
async function showBrightdataMenu() {
    console.log('\n🌟 Brightdata Browser API Konfiguration');
    console.log('==========================================');
    console.log(`Aktueller Status: ${BRIGHTDATA_CONFIG.enabled ? '✅ Aktiviert' : '❌ Deaktiviert'}`);
    console.log('\nOptionen:');
    console.log('1. Brightdata Browser API aktivieren (empfohlen für professionelle Nutzung)');
    console.log('2. Lokale Browser mit Free Proxys verwenden');
    console.log('3. Weiter ohne Änderung');
    console.log('==========================================');
    console.log('\n📝 Brightdata Vorteile:');
    console.log('   • Automatisches Proxy-Management');
    console.log('   • Integrierte Stealth- und Unblocking-Mechanismen');
    console.log('   • Automatische CAPTCHA-Lösung');
    console.log('   • Höhere Erfolgsrate');
    console.log('   • Keine manuelle Proxy-Konfiguration erforderlich');
    console.log('==========================================');

    const choice = readlineSync.question('Wählen Sie eine Option (1-3): ');

    switch (choice) {
        case '1':
            enableBrightdata();
            break;
        case '2':
            disableBrightdata();
            break;
        case '3':
            console.log(`🔄 Verwende aktuellen Status: ${BRIGHTDATA_CONFIG.enabled ? 'Brightdata' : 'Lokale Proxys'}`);
            break;
        default:
            console.log('⚠️ Ungültige Auswahl, verwende aktuellen Status');
            break;
    }
}

// Datenoptimierung-Menü
async function showDataOptimizationMenu() {
    console.log('\n📊 Datenverbrauch-Optimierung');
    console.log('================================');
    console.log(`Aktuelles Level: ${dataOptimizationLevel}`);
    console.log('\nVerfügbare Optimierungslevel:');
    console.log('1. minimal    - Nur offensichtlich unnötige Ressourcen blockieren');
    console.log('2. moderate   - Balance zwischen Datensparen und Funktionalität (empfohlen)');
    console.log('3. aggressive - Maximale Dateneinsparung');
    console.log('4. Weiter ohne Änderung');
    console.log('================================');

    const choice = readlineSync.question('Wählen Sie ein Optimierungslevel (1-4): ');

    switch (choice) {
        case '1':
            setDataOptimizationLevel('minimal');
            break;
        case '2':
            setDataOptimizationLevel('moderate');
            break;
        case '3':
            setDataOptimizationLevel('aggressive');
            break;
        case '4':
            console.log('🔄 Verwende aktuelles Level:', dataOptimizationLevel);
            break;
        default:
            console.log('⚠️ Ungültige Auswahl, verwende Standard-Level: moderate');
            setDataOptimizationLevel('moderate');
            break;
    }

    // Zeige Optimierungsdetails
    showOptimizationDetails();
}

// Zeige Details der aktuellen Optimierungseinstellungen
function showOptimizationDetails() {
    const settings = DataOptimizationConfig.levels[dataOptimizationLevel];

    console.log('\n🔧 Aktuelle Optimierungseinstellungen:');
    console.log('=====================================');
    console.log(`Level: ${dataOptimizationLevel}`);
    console.log(`Bilder blockieren: ${settings.blockImages ? '✅' : '❌'}`);
    console.log(`Fonts blockieren: ${settings.blockFonts ? '✅' : '❌'}`);
    console.log(`Analytics blockieren: ${settings.blockAnalytics ? '✅' : '❌'}`);
    console.log(`Werbung blockieren: ${settings.blockAds ? '✅' : '❌'}`);
    console.log(`Videos blockieren: ${settings.blockVideos ? '✅' : '❌'}`);
    if (settings.allowEventimImages) {
        console.log('🎫 Eventim-Bilder erlaubt (für Funktionalität)');
    }
    console.log('=====================================\n');
}

async function main() {
    // Initialisiere Proxy-Manager
    initProxyManager();

    // Lade Handler nach ProxyManager-Initialisierung
    loadHandlers();

    // Lade vorhandene Proxys aus proxys.txt (falls vorhanden)
    console.log('📂 Lade vorhandene Proxy-Liste...');
    proxyManager.loadProxiesFromFile();

    console.log(`
    Contact Me
    ¨¨¨¨¨¨¨¨¨¨
Whatsapp : +212 622 056197
Email    : <EMAIL>

`);

    // Brightdata-Konfiguration anzeigen
    await showBrightdataMenu();

    // Datenoptimierung-Menü anzeigen
    await showDataOptimizationMenu();

    console.log(`
1 : eventim.de
2 : eventim-light.com
3 : muenchenticket.de/tickets
4 : muenchenticket.net/shop`);

    let websiteChoice = null;

    while (true) {
        websiteChoice = parseInt(readlineSync.question('\nPlease enter the website number and hit enter: '));

        if (!isNaN(websiteChoice)) {
            switch (websiteChoice) {
                case 1:
                    await EventimHandler.run();
                    break;
                case 2:
                    await EventimLightHandler.run();
                    break;
                case 3:
                    await MuenchenTicketDEHandler.run();
                    break;
                case 4:
                    await MuenchenTicketNETHandler.run();
                    break;
                default:
                    console.log('Number not found');
                    continue;
            }
            break;
        } else {
            console.log('Enter a valid number. Please try again.');
        }
    }
}

// Starte Bot wenn direkt ausgeführt
if (require.main === module) {
    main();
}
