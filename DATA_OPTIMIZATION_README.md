# EventimBot - Datenverbrauch-Optimierung für bezahlte Proxys

## 🎯 Übersicht

Diese Optimierungen reduzieren den Datenverbrauch des EventimBots um **60-80%**, ohne die Bot-Funktionalität zu beeinträchtigen. Besonders wichtig für bezahlte Proxy-Anbieter, bei denen der Datenverbrauch ein Kostenfaktor ist.

## 📊 Optimierungslevel

### 1. **Minimal** 🟢
- **Dateneinsparung**: ~30-40%
- **Risiko**: Sehr niedrig
- **Empfohlen für**: Erste Tests, maximale Kompatibilität

**Was wird blockiert:**
- ✅ Analytics & Tracking (Google Analytics, etc.)
- ✅ Werbung (Google Ads, etc.)
- ✅ Social Media Widgets
- ✅ Videos
- ✅ Fonts
- ❌ Bilder (werden geladen)
- ❌ CSS (wird geladen)

### 2. **Moderate** 🟡 (Empfohlen)
- **Dateneinsparung**: ~60-70%
- **Risiko**: Niedrig
- **<PERSON>pfoh<PERSON> für**: Produktiver Einsatz

**Was wird blockiert:**
- ✅ Alle Minimal-Blockierungen
- ✅ Die meisten Bilder
- ✅ Nicht-kritische CSS
- ❌ Eventim-spezifische Bilder (für Funktionalität)
- ❌ Kritische JavaScript-Bibliotheken

### 3. **Aggressive** 🔴
- **Dateneinsparung**: ~75-85%
- **Risiko**: Mittel
- **Empfohlen für**: Maximale Kosteneinsparung

**Was wird blockiert:**
- ✅ Alle Moderate-Blockierungen
- ✅ Alle Bilder (auch Eventim-Bilder)
- ✅ Nicht-kritisches CSS
- ❌ Nur absolut notwendige Ressourcen

## 🚀 Verwendung

### Automatische Auswahl beim Start
```bash
npm start
```
Der Bot fragt beim Start nach dem gewünschten Optimierungslevel.

### Datenverbrauch-Monitor
```bash
npm run data-monitor
```
Testet verschiedene Optimierungslevel und zeigt Vergleichsberichte.

## 🔧 Technische Details

### Browser-Optimierungen
```javascript
// Zusätzliche Chrome-Flags für Datensparen
--disable-background-networking
--disable-sync
--disable-default-apps
--disable-extensions
--disable-plugins
--disable-preconnect
--disable-prefetch
--no-pings
```

### Resource-Blocking
- **Request Interception**: Blockiert Ressourcen vor dem Download
- **Intelligente Filterung**: Eventim-kritische Ressourcen werden nie blockiert
- **Kategorisierung**: Verschiedene Ressourcentypen werden unterschiedlich behandelt

### Eventim-spezifische Optimierungen
```javascript
// Kritische Domains (NIEMALS blockieren)
eventim.de, eventim.com
muenchenticket.de, muenchenticket.net
/api/, /checkout/, /cart/, /ticket/, /event/, /booking/
```

## 📈 Geschätzte Kosteneinsparungen

### Beispielrechnung für 1000 Events pro Tag:

| Level | Datenverbrauch | Einsparung | Kosten/Monat* |
|-------|----------------|------------|---------------|
| Ohne Optimierung | ~500MB/Tag | 0% | €45 |
| Minimal | ~350MB/Tag | 30% | €32 |
| Moderate | ~175MB/Tag | 65% | €16 |
| Aggressive | ~100MB/Tag | 80% | €9 |

*Beispielrechnung basierend auf €3/GB bei Premium-Proxy-Anbietern

## 🛡️ Sicherheitsfeatures

### Bot-Detection-Schutz
- User-Agent wird beibehalten
- Kritische JavaScript-Bibliotheken werden geladen
- Eventim-spezifische Ressourcen werden nie blockiert
- Stealth-Plugin bleibt aktiv

### Funktionalitäts-Schutz
```javascript
// Diese Ressourcen werden NIEMALS blockiert:
- document, xhr, fetch (für API-Calls)
- Eventim-kritische Domains
- jQuery, Bootstrap (für Website-Funktionalität)
- /api/, /checkout/, /cart/ (für Bot-Funktionen)
```

## 📊 Monitoring & Statistiken

### Live-Statistiken
```javascript
// Beispiel-Output während Bot-Lauf:
📊 Resource-Blocking Statistiken:
=====================================
Optimierungslevel: moderate
Gesamt Requests: 127
Blockiert: 89 (70.1%)
Erlaubt: 38

Blockiert nach Kategorie:
  images: 45
  analytics: 12
  ads: 8
  fonts: 15
  socialMedia: 9
=====================================
```

### Datenverbrauch-Monitor
```bash
npm run data-monitor
```
- Testet alle Optimierungslevel
- Vergleicht Dateneinsparungen
- Erstellt detaillierte Reports
- Live-Monitoring-Modus

## ⚙️ Konfiguration

### Optimierungslevel zur Laufzeit ändern
```javascript
// In eventimbot_main.js
setDataOptimizationLevel('aggressive');
```

### Eigene Optimierungsregeln
Bearbeiten Sie `data-optimization-config.js`:
```javascript
// Beispiel: Zusätzliche Domains blockieren
resourcePatterns: {
    customBlocking: [
        /example\.com/i,
        /unwanted-service\.net/i
    ]
}
```

## 🔍 Debugging

### Resource-Blocking-Logs
```bash
# Beispiel-Output:
🚫 Blockiert (images): https://static.eventim.com/banner.jpg
✅ Erlaubt (eventim-critical): https://www.eventim.de/api/events
🚫 Blockiert (analytics): https://google-analytics.com/collect
```

### Fehlerbehandlung
- Defekte Proxys werden automatisch entfernt
- Resource-Blocking-Fehler werden geloggt
- Fallback auf weniger aggressive Optimierung bei Problemen

## 📋 Best Practices

### Empfohlener Workflow
1. **Erste Tests**: Starten Sie mit `minimal`
2. **Produktiv**: Wechseln Sie zu `moderate`
3. **Kostenoptimierung**: Testen Sie `aggressive` mit kleinen Batches
4. **Monitoring**: Verwenden Sie `npm run data-monitor` regelmäßig

### Proxy-Anbieter-spezifische Tipps
- **Residential Proxys**: Verwenden Sie `moderate` (bessere Kompatibilität)
- **Datacenter Proxys**: `aggressive` ist oft möglich
- **Premium-Anbieter**: Alle Level funktionieren meist problemlos

## 🆘 Troubleshooting

### Problem: Bot funktioniert nicht mehr
**Lösung**: Reduzieren Sie das Optimierungslevel
```bash
# Starten Sie mit minimal
setDataOptimizationLevel('minimal');
```

### Problem: Zu hoher Datenverbrauch
**Lösung**: Erhöhen Sie das Optimierungslevel
```bash
# Wechseln Sie zu aggressive
setDataOptimizationLevel('aggressive');
```

### Problem: Eventim erkennt Bot
**Lösung**: Kritische Ressourcen werden automatisch erlaubt
- Prüfen Sie die Logs auf blockierte kritische Ressourcen
- Eventim-Domains werden nie blockiert
- User-Agent bleibt unverändert

## 📞 Support

Bei Problemen mit der Datenverbrauch-Optimierung:
- **WhatsApp**: +212 622 056197
- **Email**: <EMAIL>

## 🔄 Updates

### Version 1.0 (Aktuell)
- ✅ Drei Optimierungslevel
- ✅ Automatisches Resource-Blocking
- ✅ Eventim-spezifische Optimierungen
- ✅ Live-Monitoring
- ✅ Datenverbrauch-Schätzungen

### Geplante Features
- 🔄 Automatische Level-Anpassung basierend auf Proxy-Performance
- 🔄 Detaillierte Kosten-Tracking
- 🔄 Integration mit Proxy-Anbieter-APIs für echte Verbrauchsdaten
- 🔄 Machine Learning für optimale Ressourcen-Filterung
