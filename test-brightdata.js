// Test-Skript für Brightdata Browser API
const puppeteerCore = require('puppeteer-core');

const BRIGHTDATA_CONFIG = {
    endpoint: 'wss://brd-customer-hl_72945ad3-zone-scraping_browser1:<EMAIL>:9222'
};

async function testBrightdataConnection() {
    console.log('🌟 Teste Brightdata Browser API Verbindung...');
    
    try {
        console.log('📡 Verbinde mit Brightdata Endpoint...');
        const browser = await puppeteerCore.connect({
            browserWSEndpoint: BRIGHTDATA_CONFIG.endpoint,
            ignoreHTTPSErrors: true,
            timeout: 60000
        });
        
        console.log('✅ Erfolgreich mit Brightdata verbunden!');
        
        console.log('🌐 Erstelle neue Seite...');
        const page = await browser.newPage();
        
        console.log('🔗 Navigiere zu Test-URL...');
        await page.goto('https://httpbin.org/ip', { waitUntil: 'networkidle2' });
        
        console.log('📄 Hole Seiteninhalt...');
        const content = await page.content();
        console.log('📊 Seite geladen, Größe:', content.length, 'Zeichen');
        
        // Extrahiere IP-Adresse
        const ipMatch = content.match(/"origin":\s*"([^"]+)"/);
        if (ipMatch) {
            console.log('🌍 Aktuelle IP-Adresse:', ipMatch[1]);
        }
        
        console.log('🔒 Schließe Browser...');
        await browser.close();
        
        console.log('✅ Brightdata Test erfolgreich abgeschlossen!');
        return true;
        
    } catch (error) {
        console.error('❌ Brightdata Test fehlgeschlagen:', error.message);
        console.error('🔍 Fehlerdetails:', error.stack);
        return false;
    }
}

// Führe Test aus
if (require.main === module) {
    testBrightdataConnection()
        .then(success => {
            if (success) {
                console.log('\n🎉 Brightdata ist bereit für den EventimBot!');
                process.exit(0);
            } else {
                console.log('\n⚠️ Brightdata-Verbindung fehlgeschlagen. Verwende Free Proxys als Fallback.');
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('💥 Unerwarteter Fehler:', error);
            process.exit(1);
        });
}

module.exports = { testBrightdataConnection };
