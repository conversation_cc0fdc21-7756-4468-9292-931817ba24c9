// Test-Skript für Brightdata Browser API
const puppeteerCore = require('puppeteer-core');

const BRIGHTDATA_CONFIG = {
    endpoint: 'wss://brd-customer-hl_72945ad3-zone-scraping_browser1-country-de:<EMAIL>:9222',
    fallbackEndpoint: 'wss://brd-customer-hl_72945ad3-zone-scraping_browser1-country-eu:<EMAIL>:9222'
};

async function testBrightdataEndpoint(endpoint, name) {
    console.log(`📡 Teste ${name} Endpoint...`);

    try {
        const browser = await puppeteerCore.connect({
            browserWSEndpoint: endpoint,
            ignoreHTTPSErrors: true,
            timeout: 30000
        });

        console.log(`✅ Erfolgreich mit ${name} verbunden!`);

        const page = await browser.newPage();
        await page.goto('https://httpbin.org/ip', { waitUntil: 'networkidle2', timeout: 30000 });

        const content = await page.content();
        const ipMatch = content.match(/"origin":\s*"([^"]+)"/);
        if (ipMatch) {
            console.log(`🌍 ${name} IP-Adresse:`, ipMatch[1]);
        }

        await browser.close();
        return true;

    } catch (error) {
        console.error(`❌ ${name} Test fehlgeschlagen:`, error.message);
        return false;
    }
}

async function testBrightdataConnection() {
    console.log('🌟 Teste Brightdata Browser API Verbindung...');

    // Teste Deutschland Endpoint
    console.log('\n1. Teste Deutschland (DE) Endpoint:');
    const deSuccess = await testBrightdataEndpoint(BRIGHTDATA_CONFIG.endpoint, 'Deutschland');

    // Teste EU Fallback Endpoint
    console.log('\n2. Teste EU Fallback Endpoint:');
    const euSuccess = await testBrightdataEndpoint(BRIGHTDATA_CONFIG.fallbackEndpoint, 'EU');

    // Teste Eventim-Zugriff
    console.log('\n3. Teste Eventim-Zugriff:');
    const eventimSuccess = await testEventimAccess();

    const overallSuccess = deSuccess || euSuccess;

    console.log('\n📊 Test-Zusammenfassung:');
    console.log('=========================');
    console.log(`Deutschland Endpoint: ${deSuccess ? '✅' : '❌'}`);
    console.log(`EU Fallback Endpoint: ${euSuccess ? '✅' : '❌'}`);
    console.log(`Eventim-Zugriff: ${eventimSuccess ? '✅' : '❌'}`);
    console.log(`Gesamt-Status: ${overallSuccess ? '✅ Bereit' : '❌ Nicht verfügbar'}`);

    return overallSuccess;
}

async function testEventimAccess() {
    try {
        // Verwende den funktionierenden Endpoint
        const endpoint = BRIGHTDATA_CONFIG.endpoint;

        const browser = await puppeteerCore.connect({
            browserWSEndpoint: endpoint,
            ignoreHTTPSErrors: true,
            timeout: 30000
        });

        const page = await browser.newPage();

        // Teste Eventim-Zugriff
        await page.goto('https://www.eventim.de', {
            waitUntil: 'domcontentloaded',
            timeout: 30000
        });

        const title = await page.title();
        console.log('🎫 Eventim-Titel:', title.substring(0, 50) + '...');

        // Prüfe auf Access Denied
        const content = await page.content();
        const hasAccessDenied = content.includes('Access Denied') || content.includes('HTTP ERROR');

        await browser.close();

        if (hasAccessDenied) {
            console.log('⚠️ Eventim blockiert Brightdata-IPs');
            return false;
        } else {
            console.log('✅ Eventim-Zugriff erfolgreich');
            return true;
        }

    } catch (error) {
        console.error('❌ Eventim-Test fehlgeschlagen:', error.message);
        return false;
    }
}

// Führe Test aus
if (require.main === module) {
    testBrightdataConnection()
        .then(success => {
            if (success) {
                console.log('\n🎉 Brightdata ist bereit für den EventimBot!');
                process.exit(0);
            } else {
                console.log('\n⚠️ Brightdata-Verbindung fehlgeschlagen. Verwende Free Proxys als Fallback.');
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('💥 Unerwarteter Fehler:', error);
            process.exit(1);
        });
}

module.exports = { testBrightdataConnection };
