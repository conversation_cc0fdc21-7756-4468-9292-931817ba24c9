// MuenchenTicket.de Handler
const fs = require('fs');
const readlineSync = require('readline-sync');
const { sleep, clickElement, launchBrowser, createOptimizedPage, proxyManager, errorMessages, isBrightdataEnabled } = require('../eventimbot_main');

async function removeCookieBannerMuenchen(page) {
    try {
        await page.waitForSelector('.ec_button.ec_button_refuse.ec_toggleDetailView', { timeout: 5000 });
        await page.evaluate(() =>
            document.querySelector('.ec_button.ec_button_refuse.ec_toggleDetailView').click()
        );
    } catch (error) {
        // Cookie-Banner nicht gefunden
    }
}

async function addTicketsMuenchenTicketDE(page, quantity) {
    await sleep(2);
    let addToCartFailed = false;
    let availableEvents = [];
    let availableCategories = [];

    await page.waitForSelector('.detailed', { timeout: 20 * 1000 })
        .catch(() => { throw new Error('Event-list-content not found'); });

    const eventRows = await page.$$('.detailed tr');

    // Finde verfügbare Events (nicht "NotBookable")
    for (let i = 0; i < eventRows.length - 1; i++) {
        const row = eventRows[i];
        const className = await row.evaluate(el => el.className);
        const isNotBookable = className.includes('NotBookable');

        if (!isNotBookable) {
            availableEvents.push(i);
        }
    }

    if (availableEvents.length > 0) {
        // Scrolle nach unten
        await page.evaluate(() => {
            window.scrollBy(0, 400);
        });
        await sleep(2);

        // Wähle zufälliges verfügbares Event
        let selectedIndex = 0;
        if (availableEvents.length >= 10) {
            selectedIndex = availableEvents[parseInt(Math.random() * 9)];
        } else {
            selectedIndex = availableEvents[parseInt(Math.random() * availableEvents.length)];
        }

        const selectedRow = eventRows[selectedIndex];
        const bookButton = await selectedRow.$('.td_button a');

        await bookButton.click();
        console.log('Adding to cart');
        await sleep(1);

        // Warte auf Preiskategorien
        await page.waitForSelector('.api-pricecategorylist__pricecategoryitem');
        const priceCategories = await page.$$('.api-pricecategorylist__pricecategoryitem');

        // Prüfe verfügbare Ticket-Anzahl pro Kategorie
        for (let i = 0; i < priceCategories.length; i++) {
            const category = priceCategories[i];
            const options = await category.$$('[name="numberOfTickets"] option');

            const maxTickets = await options[options.length - 1].evaluate(async el =>
                await el.textContent
            );

            if (quantity <= maxTickets) {
                availableCategories.push(i);
            }
        }

        if (availableCategories.length > 0) {
            // Wähle zufällige Kategorie
            const selectedCategory = priceCategories[availableEvents[parseInt(Math.random() * availableEvents.length)]];
            const quantitySelect = await selectedCategory.$('[name="numberOfTickets"]');

            // Setze gewünschte Ticket-Anzahl
            await page.evaluate((selectElement, desiredQuantity) => {
                const quantityString = String(desiredQuantity);
                const option = selectElement.querySelector(`option[value="${quantityString}"]`);

                if (option) {
                    option.selected = true;
                    selectElement.dispatchEvent(new Event('change', { bubbles: true }));
                } else {
                    console.error(`Option with value "${quantityString}" not found`);
                }
            }, quantitySelect, quantity);
        } else {
            console.log('Selected quantity not available');
            return;
        }

        // Klicke Submit Button
        await clickElement(page, '.api-viewbestseat__submit');

        // Warte auf Checkout-Prozess
        await page.waitForSelector('.api-checkoutprocess', { timeout: 40 * 1000 })
            .catch(() => { addToCartFailed = true; });

        if (addToCartFailed) {
            console.log('Add to cart failed');
        } else {
            console.log('Done.');
        }
    } else {
        console.log('Tickets can\'t be added');
    }

    await sleep(2);
}

async function processMuenchenTicketDEEvent(eventIndex, quantity, events) {
    try {
        if (eventIndex !== 0) {
            console.log('\nNext Events =>');
        }

        const eventUrl = events[eventIndex];
        let randomProxy = null;

        // Proxy nur holen wenn Brightdata nicht aktiviert ist
        if (!isBrightdataEnabled()) {
            randomProxy = proxyManager.getRandomProxy();

            if (!randomProxy) {
                console.log('⚠️ Keine Proxys verfügbar, erzwinge Aktualisierung...');
                await proxyManager.forceUpdate();
                randomProxy = proxyManager.getRandomProxy();
                if (!randomProxy) {
                    throw new Error('Keine funktionierenden Proxys verfügbar');
                }
            }
        }

        const browser = await launchBrowser(randomProxy);
        const page = await createOptimizedPage(browser);

        console.log('📊 Datenoptimierung aktiviert für MuenchenTicket.de');
        // Keine Authentifizierung mehr nötig für Free Proxys

        await page.goto(eventUrl);

        // Prüfe auf Fehler
        const pageContent = await page.content();
        let hasError = false;

        for (const errorMessage of errorMessages) {
            if (pageContent.includes(errorMessage)) {
                hasError = true;
                break;
            }
        }

        if (hasError) {
            console.log('Access Denied, Changing IP Address..');
            // Entferne defekten Proxy aus der Liste (nur bei lokalem Proxy-Modus)
            if (!isBrightdataEnabled() && randomProxy) {
                proxyManager.removeProxy(randomProxy);
            }
            await browser.close().catch(() => null);
            await sleep(10);
            return await processMuenchenTicketDEEvent(eventIndex, quantity, events);
        }

        console.log(`\nEvent Link: ${eventUrl}`);

        await removeCookieBannerMuenchen(page);
        await addTicketsMuenchenTicketDE(page, quantity);

        try {
            await browser.close();
        } catch (error) {
            // Browser bereits geschlossen
        }

        await sleep(5);

    } catch (error) {
        try {
            await browser.close();
        } catch (closeError) {
            // Browser bereits geschlossen
        }
        console.log(error.message);
    }
}

async function run() {
    const events = fs.readFileSync('./events/muenchenticket.de_tickets.txt', 'utf-8').split('\n');
    console.log('\nEvents List:', events);

    // Benutzer-Eingaben
    let ticketQuantity;
    while (true) {
        ticketQuantity = parseInt(readlineSync.question('\nPlease enter the quantity number of tickets you would like the bot to add for each event: '));
        if (!isNaN(ticketQuantity)) break;
        console.log('The quantity should be a number. Please try again.');
    }

    let delayAfter10Events;
    while (true) {
        delayAfter10Events = parseInt(readlineSync.question('\nPlease enter the delay that the bot should wait after 10 events. Delay in seconds: '));
        if (!isNaN(delayAfter10Events)) break;
        console.log('The delay should be a number. Please try again.');
    }

    console.log('\nBot Started');

    let eventCounter = 0;

    while (true) {
        for (let i = 0; i < events.length; i++) {
            await processMuenchenTicketDEEvent(i, ticketQuantity, events);
            eventCounter++;

            if (eventCounter === 10) {
                console.log(`\n10 Events reached. Waiting for ${delayAfter10Events} seconds.\n`);
                await sleep(delayAfter10Events);
                eventCounter = 0;
            }
        }
    }
}

module.exports = { run };
