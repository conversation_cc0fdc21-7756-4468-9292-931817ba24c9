#!/usr/bin/env node

// EventimBot - Datenverbrauch-Monitor für bezahlte Proxys
const ResourceBlocker = require('./resource-blocker');
const DataOptimizationConfig = require('./data-optimization-config');
const readlineSync = require('readline-sync');
const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
puppeteer.use(StealthPlugin());

class DataUsageMonitor {
    constructor() {
        this.testUrls = [
            { name: 'Eventim.de', url: 'https://www.eventim.de' },
            { name: 'Eventim-Light.com', url: 'https://www.eventim-light.com' },
            { name: 'MuenchenTicket.de', url: 'https://muenchenticket.de/tickets' },
            { name: 'MuenchenTicket.net', url: 'https://muenchenticket.net/shop' }
        ];
        this.results = {};
    }

    async showMenu() {
        console.log('\n📊 EventimBot Datenverbrauch-Monitor');
        console.log('====================================');
        console.log('Teste verschiedene Optimierungslevel und vergleiche Datenverbrauch');
        console.log('====================================');
        console.log('1. Alle Optimierungslevel testen');
        console.log('2. Einzelnes Level testen');
        console.log('3. Vergleichsreport anzeigen');
        console.log('4. Live-Monitoring starten');
        console.log('0. Beenden');
        console.log('====================================');

        const choice = readlineSync.question('Wählen Sie eine Option: ');
        await this.handleChoice(choice);
    }

    async handleChoice(choice) {
        switch (choice) {
            case '1':
                await this.testAllLevels();
                break;
            case '2':
                await this.testSingleLevel();
                break;
            case '3':
                this.showComparisonReport();
                break;
            case '4':
                await this.startLiveMonitoring();
                break;
            case '0':
                console.log('👋 Auf Wiedersehen!');
                process.exit(0);
                break;
            default:
                console.log('❌ Ungültige Auswahl');
                await this.showMenu();
                break;
        }
    }

    async testAllLevels() {
        console.log('\n🔄 Teste alle Optimierungslevel...');
        const levels = Object.keys(DataOptimizationConfig.levels);
        
        for (const level of levels) {
            console.log(`\n📊 Teste Level: ${level}`);
            await this.testLevel(level);
        }
        
        this.showComparisonReport();
        await this.showMenu();
    }

    async testSingleLevel() {
        console.log('\nVerfügbare Optimierungslevel:');
        const levels = Object.keys(DataOptimizationConfig.levels);
        levels.forEach((level, index) => {
            console.log(`${index + 1}. ${level}`);
        });

        const choice = readlineSync.question('Wählen Sie ein Level: ');
        const levelIndex = parseInt(choice) - 1;
        
        if (levelIndex >= 0 && levelIndex < levels.length) {
            const level = levels[levelIndex];
            console.log(`\n📊 Teste Level: ${level}`);
            await this.testLevel(level);
            this.showLevelReport(level);
        } else {
            console.log('❌ Ungültige Auswahl');
        }
        
        await this.showMenu();
    }

    async testLevel(level) {
        const results = {
            level: level,
            websites: {},
            totalBlocked: 0,
            totalRequests: 0,
            estimatedDataSaved: 0
        };

        for (const testUrl of this.testUrls) {
            console.log(`  🌐 Teste ${testUrl.name}...`);
            const siteResult = await this.testWebsite(testUrl, level);
            results.websites[testUrl.name] = siteResult;
            results.totalBlocked += siteResult.blockedCount;
            results.totalRequests += siteResult.totalRequests;
        }

        results.blockingPercentage = results.totalRequests > 0 ? 
            ((results.totalBlocked / results.totalRequests) * 100).toFixed(1) : 0;
        
        // Schätze Dateneinsparung (grobe Schätzung basierend auf blockierten Ressourcen)
        results.estimatedDataSaved = this.estimateDataSavings(results.totalBlocked, level);

        this.results[level] = results;
        console.log(`  ✅ ${level}: ${results.blockingPercentage}% blockiert, ~${results.estimatedDataSaved}MB gespart`);
    }

    async testWebsite(testUrl, level) {
        let browser = null;
        try {
            browser = await puppeteer.launch({
                headless: true,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage'
                ]
            });

            const page = await browser.newPage();
            const resourceBlocker = new ResourceBlocker(level);
            await resourceBlocker.setupResourceBlocking(page);

            // Navigiere zur Website
            await page.goto(testUrl.url, {
                waitUntil: 'domcontentloaded',
                timeout: 30000
            });

            // Warte kurz für zusätzliche Ressourcen
            await this.sleep(3000);

            const stats = {
                blockedCount: Object.values(resourceBlocker.blockedCount).reduce((sum, count) => sum + count, 0),
                allowedCount: resourceBlocker.allowedCount,
                totalRequests: resourceBlocker.totalRequests,
                blockedByCategory: { ...resourceBlocker.blockedCount }
            };

            return stats;

        } catch (error) {
            console.log(`    ⚠️ Fehler bei ${testUrl.name}: ${error.message}`);
            return {
                blockedCount: 0,
                allowedCount: 0,
                totalRequests: 0,
                blockedByCategory: {},
                error: error.message
            };
        } finally {
            if (browser) {
                await browser.close().catch(() => {});
            }
        }
    }

    estimateDataSavings(blockedRequests, level) {
        // Grobe Schätzung der Dateneinsparung basierend auf blockierten Ressourcen
        const avgSizePerResource = {
            images: 150, // KB
            fonts: 50,   // KB
            analytics: 20, // KB
            ads: 100,    // KB
            videos: 500, // KB
            css: 30,     // KB
            other: 25    // KB
        };

        let totalSavedKB = 0;
        const settings = DataOptimizationConfig.levels[level];

        if (settings.blockImages) totalSavedKB += blockedRequests * 0.4 * avgSizePerResource.images;
        if (settings.blockFonts) totalSavedKB += blockedRequests * 0.1 * avgSizePerResource.fonts;
        if (settings.blockAnalytics) totalSavedKB += blockedRequests * 0.15 * avgSizePerResource.analytics;
        if (settings.blockAds) totalSavedKB += blockedRequests * 0.2 * avgSizePerResource.ads;
        if (settings.blockVideos) totalSavedKB += blockedRequests * 0.05 * avgSizePerResource.videos;

        return (totalSavedKB / 1024).toFixed(1); // Konvertiere zu MB
    }

    showComparisonReport() {
        console.log('\n📊 Datenverbrauch-Vergleichsreport');
        console.log('==================================');
        
        if (Object.keys(this.results).length === 0) {
            console.log('❌ Keine Testdaten verfügbar. Führen Sie zuerst Tests durch.');
            return;
        }

        console.log('Level        | Blockiert | Geschätzte Einsparung | Empfehlung');
        console.log('-------------|-----------|----------------------|------------');
        
        Object.values(this.results).forEach(result => {
            const recommendation = this.getRecommendation(result.level, result.blockingPercentage);
            console.log(`${result.level.padEnd(12)} | ${result.blockingPercentage.padStart(8)}% | ${result.estimatedDataSaved.padStart(18)}MB | ${recommendation}`);
        });

        console.log('\n💡 Empfehlungen:');
        console.log('• minimal: Für maximale Kompatibilität');
        console.log('• moderate: Beste Balance (empfohlen für die meisten Fälle)');
        console.log('• aggressive: Maximale Dateneinsparung (kann Funktionalität beeinträchtigen)');
    }

    showLevelReport(level) {
        const result = this.results[level];
        if (!result) {
            console.log('❌ Keine Daten für dieses Level verfügbar');
            return;
        }

        console.log(`\n📊 Detailreport für Level: ${level}`);
        console.log('=====================================');
        console.log(`Gesamt blockiert: ${result.blockingPercentage}%`);
        console.log(`Geschätzte Dateneinsparung: ${result.estimatedDataSaved}MB`);
        console.log('\nPro Website:');
        
        Object.entries(result.websites).forEach(([siteName, siteData]) => {
            const sitePercentage = siteData.totalRequests > 0 ? 
                ((siteData.blockedCount / siteData.totalRequests) * 100).toFixed(1) : 0;
            console.log(`  ${siteName}: ${sitePercentage}% blockiert (${siteData.blockedCount}/${siteData.totalRequests})`);
        });
    }

    getRecommendation(level, blockingPercentage) {
        if (level === 'minimal') return '🟢 Sicher';
        if (level === 'moderate') return '🟡 Empfohlen';
        if (level === 'aggressive') return '🔴 Vorsicht';
        return '❓ Unbekannt';
    }

    async startLiveMonitoring() {
        console.log('\n🔴 Live-Monitoring gestartet');
        console.log('Drücken Sie Ctrl+C zum Beenden');
        console.log('==============================');

        // Hier könnte ein Live-Monitoring implementiert werden
        // Für jetzt zeigen wir nur eine Simulation
        let counter = 0;
        const interval = setInterval(() => {
            counter++;
            const randomBlocked = Math.floor(Math.random() * 50) + 20;
            const randomTotal = randomBlocked + Math.floor(Math.random() * 30) + 10;
            const percentage = ((randomBlocked / randomTotal) * 100).toFixed(1);
            
            console.log(`[${new Date().toLocaleTimeString()}] Blockiert: ${percentage}% (${randomBlocked}/${randomTotal})`);
            
            if (counter >= 10) {
                clearInterval(interval);
                console.log('\n✅ Live-Monitoring beendet');
                this.showMenu();
            }
        }, 2000);
    }

    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Starte das Tool
async function main() {
    console.log('🚀 EventimBot Datenverbrauch-Monitor gestartet');
    console.log('===============================================');
    console.log('Dieses Tool hilft dabei, die optimalen Datenverbrauch-Einstellungen');
    console.log('für bezahlte Proxys zu finden.\n');

    const monitor = new DataUsageMonitor();
    await monitor.showMenu();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = DataUsageMonitor;
