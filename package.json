{"name": "eventimbot", "version": "1.0.0", "description": "EventimBot - Automated ticket booking bot", "main": "eventimbot_main.js", "scripts": {"start": "node eventimbot_main.js", "proxy-tool": "node proxy-tool.js", "scrape-proxies": "node scrape-proxies.js", "test-proxy-compatibility": "node test-proxy-compatibility.js", "test-proxy": "node test-proxy-system.js", "test-scraper": "node test-scraper.js", "demo-proxy": "node demo-proxy-system.js", "data-monitor": "node data-usage-monitor.js", "test": "jest", "test:verbose": "jest --verbose"}, "dependencies": {"axios": "^1.9.0", "proxifly": "^2.0.2", "puppeteer": "^24.9.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "readline-sync": "^1.4.10"}, "keywords": ["eventim", "tickets", "automation", "bot"], "author": "<EMAIL>", "license": "MIT", "devDependencies": {"jest": "^29.7.0"}}